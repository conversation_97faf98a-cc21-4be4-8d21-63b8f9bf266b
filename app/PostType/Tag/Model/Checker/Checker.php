<?php

namespace App\PostType\Tag\Model\Checker;

use App\Model\Orm\PriceLevel\PriceLevelRepository;
use App\Model\Orm\Product\ProductRepository;
use App\Model\Orm\ProductLocalization\ProductLocalization;
use App\PostType\Tag\Model\Orm\Tag\TagRepository;
use App\PostType\Tag\Model\TagType;
use Closure;

class Checker
{

	public function __construct(
		private readonly ProductRepository $productRepository,
		private readonly PriceLevelRepository $priceLevelRepository,
		private readonly TagRepository $tagRepository,
	)
	{
	}

	/**
	 * @phpstan-return  Closure(ProductLocalization $productLocalization): bool
	 */
	public function getCheckerFunction(TagType $tagType): Closure
	{
		return match ($tagType) {
			TagType::new => fn (ProductLocalization $productLocalization): bool => $this->productRepository->isNewProductByDateCreated($productLocalization->product),
			TagType::transitFree => fn (ProductLocalization $productLocalization): bool => $this->productRepository->getProductWithFreeTransport()->getById($productLocalization->product->id) !== null,
			TagType::paidByLo => fn (ProductLocalization $productLocalization): bool => $this->hasPaidByLo($productLocalization),
			TagType::paidByLoFull => fn (ProductLocalization $productLocalization): bool => $this->hasPaidByLoFull($productLocalization),
			TagType::giftFree => fn (ProductLocalization $productLocalization): bool => $this->hasPresent($productLocalization),
			TagType::promoPrice => fn (ProductLocalization $productLocalization): bool => $this->hasPromoPrice($productLocalization),
			default => fn (ProductLocalization $productLocalization): bool => false,
		};
	}

	private function hasPromoPrice(ProductLocalization $productLocalization): bool
	{
		$mutation = $productLocalization->mutation;
		$priceLevelDefault = $this->priceLevelRepository->getDefault();
		$productLocalization->product->setMutation($mutation);

		return $productLocalization->product->hasPromoPrice($mutation, $priceLevelDefault, $mutation->states->toCollection()->fetch());
	}

	private function hasPaidByLo(ProductLocalization $productLocalization): bool
	{
		return $productLocalization->hasBuyableRequalificationPossibility($productLocalization->mutation->getFirstState());
	}

	private function hasPresent(ProductLocalization $productLocalization): bool
	{
		return $this->tagRepository->findProductsWithPresent()->getById($productLocalization->product->id) !== null;
	}

	private function hasPaidByLoFull(ProductLocalization $productLocalization): bool
	{
		return $productLocalization->hasRequalificationPossibility($productLocalization->mutation->getFirstState()) && $productLocalization->requalificationPossibility === 100;
	}

}
