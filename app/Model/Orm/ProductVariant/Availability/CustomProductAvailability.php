<?php

declare(strict_types=1);

namespace App\Model\Orm\ProductVariant\Availability;


use App\Model\DeliveryDate;
use App\Model\Orm\DeliveryMethod\DeliveryMethodConfiguration;
use App\Model\Orm\Holiday\Holiday;
use App\Model\Orm\Holiday\HolidayModel;
use App\Model\Orm\Mutation\Mutation;
use App\Model\Orm\Order\Delivery\DeliveryType;
use App\Model\Orm\Orm;
use App\Model\Orm\PriceLevel\PriceLevel;
use App\Model\Orm\State\State;
use App\Model\Orm\Stock\Stock;
use App\Model\Orm\Supply\DTO\SupplyDTO;
use App\Model\Orm\Traits\HasStaticCache;
use App\Model\Setup;
use App\Model\Time\CurrentDateTimeProvider;
use App\Model\Time\DeliveryTiming;
use App\Model\Time\StoreTiming;
use App\Model\TranslateData;
use App\PostType\Supplier\Model\Orm\Supplier\Supplier;
use App\PostType\Supplier\Model\Orm\Supplier\SupplierModel;
use App\Utils\DateTime;
use Brick\Money\Currency;
use Nette\Utils\Strings;
use Nextras\Dbal\Utils\DateTimeImmutable;
use Stringable;

final class CustomProductAvailability extends AbstractProductAvailability
{
	use HasStaticCache;
	public const TYPE_ON_STOCK = 'onstock'; // Skladem na prodejne
	public const TYPE_ON_STOCK_SUPPLIER = 'onstock_supplier'; // Skladem do x dnu;
	public const TYPE_TO_ORDER = 'to_order'; // Na objednavku
	public const TYPE_WAITING = 'waiting'; // Očekávame
	public const TYPE_PREORDER = 'preorder'; // Predobjednávky
	public const TYPE_OUT_OF_STOCK = 'out_of_stock'; // Dočasne vyprodano
	public const TYPE_NOT_FOR_SALE = 'not_for_sale'; // Trvale vyprodáno
	public const TYPE_TO_DOWNLOAD = 'to_download'; // Ihned k stažení

	/** @var array<SupplyDTO> */
	protected ?array $supplies = null;
	protected ?array $storeOpeningHours = null;
	protected ?array $supplierOpeningHours = null;
	protected bool $showAddCart = true;
	protected bool $showWatchdog = false;
	protected bool $showSimilar = false;
	protected ?string $type = null;

	protected ?DateTimeImmutable $dateStock = null;

	protected ?DateTimeImmutable $dateStore = null;
	protected ?string $availabilityStateText = null;
	protected string|Stringable|null $availabilityText = null;
	protected string|Stringable|null $availabilityShortText = null;
	protected string|Stringable|null $deliveryText = null;
	protected string|Stringable|null $infoText = null;

	protected string|Stringable|null $storeText = null;

	private array $initialized = [];


	public function __construct(
		private readonly HolidayModel $holidayModel,
		private readonly Orm $orm,
		private readonly CurrentDateTimeProvider $currentDateTimeProvider,
		private readonly SupplierModel $supplierModel,
		private readonly StoreTiming $storeTiming,
		private readonly DeliveryTiming $deliveryTiming,
	) {
	}



	private function isInitialized(int $quantityRequired): bool
	{
		return array_key_exists($this->createKey($quantityRequired), $this->initialized);
	}

	private function setInitialized(int $quantityRequired): void
	{
		$this->initialized[$this->createKey($quantityRequired)] = clone $this;
	}

	private function getInitialized(int $quantityRequired = 1): self
	{
		return $this->initialized[$this->createKey($quantityRequired)];
	}

	private function createKey(int $quantityRequired): string
	{
		return md5('init' . $this->productVariant->id . $quantityRequired);
	}

	private function setAvailabilityText(string $text, array $parameters = []): void
	{
		$this->availabilityText = new TranslateData($text, $parameters);
	}

	private function setAvailabilityShortText(string $shortText, array $parameters = []): void
	{
		$this->availabilityShortText = new TranslateData($shortText,$parameters);
	}

	private function setInformationText(string $text, array $parameters = []): void
	{
		$this->infoText = new TranslateData($text, $parameters);
	}
	private function setDeliveryText(string $text, array $parameters = []): void
	{
		$this->deliveryText = new TranslateData($text, $parameters);
	}

	private function init(int $quantityRequired = 1): void
	{

		if ($this->isInitialized($quantityRequired)) {
			return;
		}

		if ($this->supplies === null) {
			$this->supplies = $this->productVariant->suppliesByStockAlias;
		}


		if ($this->product->productTypeId === 999) {
			$this->type      = self::TYPE_ON_STOCK;
			$this->setAvailabilityText('stock_available');
			$this->setAvailabilityShortText('stock_available_short');
			$this->availabilityStateText = '';
			$this->dateStock = $this->currentDateTimeProvider->getCurrentDateTime();
			$this->setInitialized($quantityRequired);

			$this->showAddCart = true;
			$this->showSimilar = false;
			$this->showWatchdog = false;

			return;
		}



		if ($this->product->isElectronic) {
			$this->type      = self::TYPE_TO_DOWNLOAD;
			$this->setAvailabilityText('stock_to_download');  // Ke stažení
			$this->setAvailabilityShortText('stock_to_download_short');  // Ke stažení
			$this->availabilityStateText = 'Ke stažení';
			$this->dateStock = $this->currentDateTimeProvider->getCurrentDateTime();
			$this->setInitialized($quantityRequired);
			return;
		}

		if ($this->getShopSupply() !== null && ($stockAmount = $this->getShopSupply()->amount) > 0 &&  $stockAmount >= $quantityRequired) {
			$this->type      = self::TYPE_ON_STOCK;
			$this->dateStock = $this->calculateStockDate($this->currentDateTimeProvider->getCurrentDateTime(), true);
			$this->dateStore = $this->storeTiming->getFutureClosingTime($this->product, $this->currentDateTimeProvider->getCurrentDateTime());
			$this->setAvailabilityText('stock_available', ['%s' => $this->getCountText($stockAmount)]);//'Skladem X ks'
			$this->setAvailabilityShortText('stock_available_short'); //'Skladem';
			$this->availabilityStateText = 'Skladem';

		} elseif ($this->getSupplierSupply() !== null && ($stockSupplier = $this->getSupplierSupply()->amount) > 0 && $this->getTotalSupplyCount() >= $quantityRequired) {
			$this->availabilityStateText = 'Skladem u dodavatele';
			$diffDays = $this->getSupplierDiffDays();
			$this->type = self::TYPE_ON_STOCK_SUPPLIER;

			if($diffDays === null){
				$this->setAvailabilityText('stock_supplier'); //'Skladem u dodavatele';
				$this->setAvailabilityShortText('stock_supplier_short'); //'U dodavatele';
				$this->dateStore = $this->storeTiming->getFutureClosingTime($this->product, $this->currentDateTimeProvider->getCurrentDateTime());
				$this->dateStock = $this->calculateStockDate($this->currentDateTimeProvider->getCurrentDateTime(), true);
				$this->setInitialized($quantityRequired);
				return;
			}

			$isStockNull = (bool) $this->product->isStockNull;

			if ($diffDays === 1) {
				$this->setAvailabilityText($isStockNull ? 'stock_supplier_tomorrow_null' : 'stock_supplier_tomorrow', ['%stock' => $this->getCountText($stockSupplier)]);//'Skladem zítra X ks';
				$this->setAvailabilityShortText('stock_supplier_short_tomorrow'); //'Do 1 dne';
			} elseif ($diffDays === 2) {
				$this->setAvailabilityText($isStockNull ? 'stock_supplier_after_tomorrow_null' : 'stock_supplier_after_tomorrow', ['%stock' => $this->getCountText($stockSupplier)]); //'Skladem pozítří X ks';
				$this->setAvailabilityShortText('stock_supplier_short_after_tomorrow');//'Do 2 dnů';
			} elseif ($diffDays > 2 && $diffDays < 6) {
				$this->setAvailabilityText($isStockNull ? 'stock_supplier_days_null' : 'stock_supplier_days', ['%stock' => $this->getCountText($stockSupplier), '%s' => $diffDays]); //'Skladem do Y dnů X ks';
				$this->setAvailabilityShortText('stock_supplier_days_short', ['%stock' => $this->getCountText($stockSupplier), '%s' => $diffDays]); //'Do Y dnů';
			} elseif ($diffDays > 5 && $diffDays < 11) {
				$this->setAvailabilityText($isStockNull ? 'stock_supplier_week_null' : 'stock_supplier_week', ['%stock' => $this->getCountText($stockSupplier)]); //'Skladem do týdne X ks';
				$this->setAvailabilityShortText('stock_supplier_short_week'); //'Do týdne';
			} else {
				$this->setAvailabilityText('stock_supplier', ['%stock' => $this->getCountText($stockSupplier)]); //'Skladem u dodavatele';
				$this->setAvailabilityShortText('stock_supplier_short');//'U dodavatele';
			}

			if ($this->getSupplierSupply() !== null && ($supplierStockDate = $this->getSupplierSupply()->stockDate) !== null && $diffDays > 10) {
				$ct = $this->currentDateTimeProvider->getCurrentDateTime()->modify('+ ' . $diffDays . ' days');
				$this->setAvailabilityText($isStockNull ? 'stock_supplier_from_null' : 'stock_supplier_from', ['%ct' => $ct->format('j. n'), '%from' => $supplierStockDate->format('j. n'),'%stock' => $this->getCountText($stockSupplier)]); //'Skladem od [očekávané datum naskladnění]';
				$this->setAvailabilityShortText('stock_supplier_short_from', ['%ct' => $ct->format('j. n'),'%from' => $supplierStockDate->format('j. n')]); //'Skladem od [očekávané datum naskladnění]';
			}

			$this->dateStore = $this->storeTiming->getFutureClosingTime($this->product, ($this->currentDateTimeProvider->getCurrentDateTime())->modify('+' . $diffDays .' day'));
			$this->dateStock = $this->calculateStockDate(($this->currentDateTimeProvider->getCurrentDateTime())->modify('+' . $diffDays . ' day'), true);

		} elseif ($this->productVariant->product->isInPrepare && !$this->productVariant->isOld) {
			// Predobjednavky 3 - nevime OTN
			$this->availabilityStateText = 'Předobjednávka';
			$this->setAvailabilityText('stock_preorder3');
			$this->setAvailabilityShortText('stock_preorder3_short');
			$this->showAddCart = false;
			$this->showSimilar = true;
			$this->showWatchdog = true;
			$this->type = self::TYPE_OUT_OF_STOCK;
			$this->dateStock = null;

			$stockDate = $this->getSupplierSupply()?->stockDate;

			if ($stockDate !== null) {
				$this->showAddCart = true;
				$date = $this->currentDateTimeProvider->getCurrentDateTime();
				$diff = $stockDate->diff($date);
				$beforeWorkingDays = 2;

				$this->type = self::TYPE_PREORDER;
				$this->showAddCart = true;
				$this->showSimilar = false;
				$this->showWatchdog = false;
				/*$beforeWorkingDays = $this->getNumOfWorkingDays($stockDate, 2, true);
				$afterWorkingDays  = $this->getNumOfWorkingDays($stockDate, 3);

				if (($diff->invert && $diff->days <= $beforeWorkingDays) || ( ! $diff->invert && $diff->days >= 0 && $diff->days < $afterWorkingDays)) {
					$this->availabilityStateText = 'Očekáváme';
					$this->deliveryText     = $this->translate('delivery_on_way'); //'Očekáváme naskladnění každým dnem';
					$this->availabilityText = $this->translate('stock_on_way'); //'Na cestě';
					$this->type = self::TYPE_PREORDER;
					$this->setInitialized($quantityRequired);
					return;
				} elseif ( ! $diff->invert && $diff->days >= $afterWorkingDays) {
					$this->showAddCart      = false;
					$this->showWatchdog     = true;
					$this->deliveryText     = $this->translate('delivery_temporary_unavailable'); //'Datum naskladnění prověřujeme';
					$this->availabilityText = $this->translate('stock_temporary_unavailable'); //'Dočasně vyprodáno';
					$this->type = self::TYPE_OUT_OF_STOCK;
					$this->setInitialized($quantityRequired);
					return;
				}*/

				$this->dateStock = $stockDate;

				if ((!$diff->invert && $diff->days <= $beforeWorkingDays)) {
					// Predobjednavky 2
					$this->setAvailabilityText('stock_preorder2');
					$this->setAvailabilityShortText('stock_preorder2_short');
					$this->setInformationText('stock_preorder2_info');
				} elseif ((!$diff->invert && $diff->days > $beforeWorkingDays)) {
					// Predobjednavky 3
					$this->setAvailabilityText('stock_preorder3');
					$this->setAvailabilityShortText('stock_preorder3_short');
					$this->showAddCart = false;
					$this->showSimilar = true;
					$this->showWatchdog = true;
					$this->type = self::TYPE_OUT_OF_STOCK;
					$this->dateStock = null;
				} else {
					// Predobjednavky 1
					$this->setAvailabilityText('stock_preorder');//'Možné předobjednávky';
					$this->setAvailabilityShortText('stock_preorder_short', ['%from' => $stockDate->format('j. n')]);//'Očekáváme %s
					$this->setDeliveryText('delivery_preorder', ['%s' => $stockDate->format("j. n.")]);//'Očekáváme naskladnení %s
				}

			}

		} elseif ($this->productVariant->isOld) {
			$this->availabilityStateText = 'Prodej ukončen';
			$this->setDeliveryText('delivery_out_of_stock'); //'Momentálně nemáme informaci o dotisku';
			$this->setAvailabilityText('stock_out_of_stock');// 'Produkt je vyprodaný';
			$this->showAddCart = false;
			$this->showWatchdog = false;
			$this->showSimilar = true;
			$this->type = self::TYPE_NOT_FOR_SALE;

		} else {
			$stockDate               = $this->getSupplierSupply()?->stockDate;

			// Očekávame 1,2,3
			if ($stockDate !== null) {
				$this->availabilityStateText = 'Očekávame';
				$this->infoText = null;

				$this->type = self::TYPE_WAITING;

				$date = $this->currentDateTimeProvider->getCurrentDateTime();
				$diff = $stockDate->diff($date);

				$beforeWorkingDays = 2; //$this->getNumOfWorkingDays($stockDate, 2, true);

				if ((!$diff->invert && $diff->days <= $beforeWorkingDays)) {
					// Ocekavame 2
					$this->setAvailabilityText('stock_waiting2');
					$this->setAvailabilityShortText('stock_waiting2_short');
					$this->setInformationText('stock_waiting2_info');
				} elseif ((!$diff->invert && $diff->days > $beforeWorkingDays)) {
					// Ocekavame 3
					$this->setAvailabilityText('stock_waiting3');
					$this->setAvailabilityShortText('stock_waiting3_short');
					$this->showAddCart = false;
					$this->showSimilar = true;
					$this->showWatchdog = true;
					$this->type = self::TYPE_OUT_OF_STOCK;
					$this->dateStock = null;
				} else {
					// Ocekavame 1;
					$this->setAvailabilityText('stock_waiting1',['%from' => $stockDate->format('j. n')]);
					$this->setAvailabilityShortText('stock_waiting1_short', ['%from' => $stockDate->format('j. n')]);
				}

				$this->getSupplierDiffDays(true);
				$this->dateStore = null;
				$this->dateStock = null;
				$this->setInitialized($quantityRequired);
				return;
			}

			$lastOnStockDateSupplier = $this->getSupplierSupply()?->lastOnStock;
			$lastOnStockDateShop     = $this->getShopSupply()?->lastOnStock;

			$lastOnStocks = [
					$lastOnStockDateShop?->getTimestamp() ?? 1     => $lastOnStockDateShop,
					$lastOnStockDateSupplier?->getTimestamp() ?? 0 => $lastOnStockDateSupplier
			];

			$lastOnStockDate = $lastOnStocks[max(array_keys($lastOnStocks))];

			// Vyprodáno
			if ($lastOnStockDate === null) {
				// TODO: petr zisti u klienta / alebo vyplnit fiktivni datum
				$this->availabilityStateText = 'Vyprodáno';
				$this->setAvailabilityText('stock_temporary_unavailable');//'Dočasně vyprodáno';
				$this->setAvailabilityShortText('stock_temporary_unavailable_short');// 'Vyprodáno'
				$this->setInformationText('stock_temporary_unavailable_info');
				$this->showAddCart = false;
				$this->showWatchdog = true;
				$this->showSimilar = true;
				$this->type = self::TYPE_OUT_OF_STOCK;
				$this->setInitialized($quantityRequired);
				return;
			}

			$date = $this->currentDateTimeProvider->getCurrentDateTime();
			$diff = $date->diff($lastOnStockDate);

			if ($diff->invert && $diff->days < 180) {
				$this->availabilityStateText = 'Vyprodáno';
				$this->setAvailabilityText('stock_temporary_unavailable');//'Dočasně vyprodáno';
				$this->setAvailabilityShortText('stock_temporary_unavailable_short');// 'Vyprodáno'
				$this->setInformationText('stock_temporary_unavailable_info');
				$this->showAddCart = false;
				$this->showSimilar = true;
				$this->showWatchdog = true;
				$this->type = self::TYPE_OUT_OF_STOCK;
				$this->setInitialized($quantityRequired);
				return;
			}

			// Prodej ukončen
			$this->setDeliveryText('delivery_out_of_stock');//'Momentálně nemáme informaci o dotisku';
			$this->setAvailabilityText('stock_out_of_stock');//'Prodej ukončen';
			$this->availabilityStateText = 'Prodej ukončen';
			$this->showAddCart = false;
			$this->showWatchdog = false;
			$this->showSimilar = true;
			$this->type = self::TYPE_NOT_FOR_SALE;

		}
		$this->setInitialized($quantityRequired);
	}

	public function getAvailabilityText(int $quantityRequired = 1): Stringable|string|null
	{
		$this->init($quantityRequired);
		$availability = $this->getInitialized($quantityRequired);
		return $availability->availabilityText;
	}

	public function getAvailabilityShortText(): Stringable|string|null
	{
		$this->init();
		$availability = $this->getInitialized();
		return $availability->availabilityShortText ?? $availability->availabilityText;
	}

	public function getAvailabilityStateText(): string
	{
		$this->init();
		$availability = $this->getInitialized();
		return $availability->availabilityStateText ?? 'Neznáma';
	}

	public function getDeliveryText(
		Mutation $mutation,
		State $state,
		PriceLevel $priceLevel,
		Currency $currency,
		?DeliveryMethodConfiguration $deliveryMethodConfiguration = null,
		int $quantityRequired = 1
	): string|\Stringable|null{
		$deliveryDate = $this->getDeliveryDate($mutation, $state, $priceLevel, $currency, $deliveryMethodConfiguration, $quantityRequired);
		if ($deliveryDate!== null) {
			$day = $deliveryDate->from->format('j. n.');
			$date = $deliveryDate->from->format('j. n. Y');
			$key = match($this->type) {
				self::TYPE_TO_ORDER => 'delivery_text_to_order',
				default => 'delivery_text',
			};
			return new TranslateData($key, ['%s' => $day, '%day' => $this->formatDateToString($deliveryDate->from), '%date' => $date]);
		}

		return $this->deliveryText;
	}

	public function getExpeditionText(
		Mutation $mutation,
		State $state,
		PriceLevel $priceLevel,
		Currency $currency,
		?DeliveryMethodConfiguration $deliveryMethodConfiguration = null,
		int $quantityRequired = 1
	): ?string {
		// TODO: Implement getExpeditionText() method.

		return null;
	}

	public function getBestDeliveryMethod(Mutation $mutation, State $state, PriceLevel $priceLevel,	Currency $currency): ?DeliveryMethodConfiguration
	{
		return $this->loadCache($this->createCacheKey('best_delivery_method', ... func_get_args()), function () use($mutation, $state, $priceLevel, $currency): ?DeliveryMethodConfiguration {
			$methods = [];

			$today = new DateTime('today midnight');
			$i = 0;
			/** @var DeliveryMethodConfiguration $method */
			foreach ($this->orm->deliveryMethod->getAvailable($mutation, $state, $priceLevel, $currency)->fetchPairs('id') as $method) {
				if ($method->getDeliveryMethod()->getDeliveryType() === DeliveryType::Store) {
					continue;
				}
				try {
					$closingTime = $method->deliveryHourByStock->{Stock::ALIAS_SHOP} ?? '07:00';
					$closingDate = $today->modifyClone($closingTime);
					$methods[$closingDate->getTimestamp() + $i] = $method;
					$i++;
				} catch (\Throwable $e) {
					// do nothing - bad data from administrator
				}

			}

			krsort($methods);

			$method = reset($methods);

			return $method instanceof DeliveryMethodConfiguration ? $method : null;
		});
	}
	public function getDeliveryDate(
		Mutation $mutation,
		State $state,
		PriceLevel $priceLevel,
		Currency $currency,
		?DeliveryMethodConfiguration $deliveryMethodConfiguration = null,
		int $quantityRequired = 1
	): ?DeliveryDate {

		$this->init($quantityRequired);
		$availability = $this->getInitialized($quantityRequired);

		if ($availability->type === self::TYPE_PREORDER) {
			return null;
		}

		if ($availability->dateStock === null) {
			return null;
		}

		$dateExpedition = DateTime::from($availability->dateStock);

		/** @var Holiday $holiday */
		foreach ($this->holidayModel->getAll() as $holiday) { // iterujeme chronologicky vsechny platne dovolene
			$holidayEnd = $holiday->publicTo->modify('tomorrow midnight');
			if ($dateExpedition <= $holidayEnd) { // DD je v terminu dovolene
				$dateExpedition = DateTime::from($holidayEnd)->setHolidays($state)->getClosestWorkday(); // DD je nejblizsi prac. den po dovolene
			}
		}

		$dateFrom = clone $dateExpedition;
		$dateTo = null;

		if ($deliveryMethodConfiguration === null) {
			$deliveryMethodConfiguration = $this->getBestDeliveryMethod($mutation, $state, $priceLevel, $currency);

			// if combination of mutation, state, priceLevel and currency doesn't have any delivery methods - add 2 workdays by default
			if ($deliveryMethodConfiguration === null) {
				$dateFrom->addWorkday(2);
				return new DeliveryDate($dateFrom, $dateTo, $dateExpedition);
			}
		}

		$crucialTime = $this->deliveryTiming->getCrucialTimeForDelivery($availability, $quantityRequired, $deliveryMethodConfiguration);

		try {
			/** @throws \DateMalformedStringException */
			$dateFrom->modify($crucialTime);
		} catch (\Throwable $e) {
			// do nothing - bad input from administrator
		}
		$dateFrom->getClosestWorkday();



		if ($dateFrom->isPast($this->currentDateTimeProvider->getCurrentDateTime())) {
			$dateFrom->addWorkday();
		}

		if (($deliveryDayFrom = $deliveryMethodConfiguration->deliveryDayFrom) > 0) {
			$dateFrom->setHolidays($state);
			$dateFrom->addWorkday($deliveryDayFrom);
		}

		if (($deliveryDayTo = $deliveryMethodConfiguration->deliveryDayTo) > 0) {
			$dateTo = $dateFrom->modifyClone(sprintf('+ %d day', $deliveryDayTo));
			$dateTo->setHolidays($state);
			$dateTo->getClosestWorkday();
		}

		return new DeliveryDate($dateFrom, $dateTo, $dateExpedition);
	}

	public function getStockDate(): \DateTimeImmutable
	{
		$this->init();
		$availability = $this->getInitialized();
		return $this->getSupplierSupply()?->stockDate ?? $availability->dateStock ?? $this->currentDateTimeProvider->getCurrentDateTime();
	}

	public function getInfoText(): string|\Stringable|null
	{
		$this->init();
		$availability = $this->getInitialized();
		return $availability->infoText;
	}

	public function getStoreDate(): string|\Stringable
	{
		$this->init();
		$availability = $this->getInitialized();

		if ($availability->dateStore === null) {
			return '';
		}

		$day = $this->formatDateToString($availability->dateStore);
		$diff = $this->currentDateTimeProvider->getCurrentDateTime()->diff($availability->dateStore);

		if ($day === '' || $diff->days > 5) {
			$day = trim($day . ' ' . $availability->dateStore->format("j. n."));
		}

		return $day;
	}

	public function getStoreText(State $state): string|\Stringable|null
	{
		$this->init();
		$availability = $this->getInitialized();
		if ($availability->dateStore !== null) {
			$day = $this->formatDateToString($availability->dateStore);

			$diff = $this->currentDateTimeProvider->getCurrentDateTime()->diff($availability->dateStore);
			if ($day === '' || $diff->days > 5) {
				$day = trim($availability->dateStore->format("j. n."));
			}

			return new TranslateData('store_text', ['%s' => $day, '%day' => $this->formatDateToString($availability->dateStore)]); //'nebo už ' . $day . ' na prodejně Praha';
		}
		return $availability->storeText;
	}

	public function isShowCartCatalog(Mutation $mutation, PriceLevel $priceLevel, State $state): bool
	{
		$this->init();
		$availability = $this->getInitialized();
		return $availability->showAddCart && !$this->product->hasPriceFrom($mutation, $priceLevel, $state);
	}

	public function isShowCartDetail(Mutation $mutation, PriceLevel $priceLevel, State $state): bool
	{
		$this->init();
		$availability = $this->getInitialized();
		//if($this->isVariantSetted()) {
			//return $this->showAddCart && $this->variant->totalSupplyCount > 0;
		//}

		return $availability->showAddCart;
	}

	public function isShowWatchdog(): bool
	{
		$this->init();
		$availability = $this->getInitialized();
		return $availability->showWatchdog;
	}

	public function isShowSimilar(): bool
	{
		$this->init();
		$availability = $this->getInitialized();
		return $availability->showSimilar;
	}

	public function hasPrice(Mutation $mutation, PriceLevel $priceLevel, State $state): bool
	{
		return $this->product->price($mutation, $priceLevel, $state)->isGreaterThan(0);
	}

	public function getMaxAvailableAmount(): int
	{
		if ( ! $this->product->public) {
			return 0;
		}

		$this->init();
		$availability = $this->getInitialized();

		if (in_array($availability->getType(), [self::TYPE_PREORDER, self::TYPE_WAITING])) {
			return 100;
		}

		return $availability->getTotalSupplyCount();
	}

	public function getType(): ?string
	{
		$this->init();
		$availability = $this->getInitialized();
		return $availability->type;
	}


	/**    CUSTOM METHODS       */

	public function getSupplierDiffDays(bool $addClosingDays = false): ?int
	{
		$stockDate     = $this->getSupplierSupply()?->stockDate;
		$date = $this->currentDateTimeProvider->getCurrentDateTime();

		$standardDays        = $this->getSupplier()?->getStandardDays() ?? 8;
		$closingAddDays      = $this->getSupplier()?->getClosingAddDays()  ?? 0;

		$supplierClosingTime = $this->getSupplier()?->getClosingTime();

		if ($supplierClosingTime === null) {
			return null;
		}

		if ($stockDate !== null) {
			$dayInWeek = (int) $stockDate->format('N') - 1;
			$dateAdded = $stockDate;
			$diff = $date->diff($stockDate);
			$diffDays = $diff->days;

		} else {
			$dayInWeek = (int) date('N') - 1;
			$dateAdded = $date;
			$diffDays = 0;
		}

		$supplierClosingHour = $supplierClosingTime[$dayInWeek] ?? null;
		if ($supplierClosingHour === null || (int) $date->format('G') >= (int) $supplierClosingHour) {
			$standardDays += $this->getNextSupplierClosingDayFromToday($dayInWeek + 1);
		}

		if($addClosingDays){
			$standardDays += $closingAddDays;
		}

		$this->dateStock = $this->calculateStockDate($dateAdded->modify('+' . $standardDays . ' days'));
		$this->dateStore = $this->storeTiming->getFutureClosingTime($this->product, $dateAdded->modify('+' . $standardDays . ' days'));

		$diffDays += $standardDays;

		return $diffDays;
	}


	public function getShopSupply(): ?SupplyDTO
	{
		if ($this->supplies === null) {
			$this->supplies = $this->productVariant->suppliesByStockAlias;
		}
		return $this->supplies[Stock::ALIAS_SHOP] ?? null;
	}

	public function getTotalSupplyCount(): int
	{
		$shopAmount = $this->getShopSupply()->amount ?? 0;
		$supplierAmount = $this->getSupplierSupply()->amount ?? 0;
		return $shopAmount + $supplierAmount;
	}

	private function calculateStockDate(DateTimeImmutable $onStockDate, bool $withWeekend = false): DateTimeImmutable
	{
		$deliveryDate = $onStockDate;//->modify("+1 day");
		if ( ! $withWeekend) {
			$addDays      = $this->getNumOfWorkingDays($deliveryDate, 1);
			$deliveryDate = $deliveryDate->modify('+' . $addDays . ' days');
		}

		return $deliveryDate;
	}

	private function getNumOfWorkingDays(DateTimeImmutable $date, int $number, bool $before = false): int
	{
		$i   = 1;
		$add = 1;
		do {
			if ($before) {
				$newDate = $date->modify('-' . $add . ' day');
			} else {
				$newDate = $date->modify('+' . $add . ' day');
			}
			if ( ! in_array($newDate->format('w'), ['0', '6'])) {
				$i++;
			}
			$add++;
		} while ($i <= $number);

		$add--;

		return $add;
	}

	private function getCountText(int $stockAmount): string
	{
		$count = '';
		if ($stockAmount <= 5) {
			$count = $stockAmount . 'ks';
		} else {
			if ($stockAmount > 50) {
				$count = '>50ks';
			} elseif ($stockAmount > 20) {
				$count = '>20ks';
			} else {
				$count = '>5ks';
			}
		}

		return $count;
	}

	private function getSupplierSupply(): ?SupplyDTO
	{
		if ($this->supplies === null) {
			$this->supplies = $this->productVariant->suppliesByStockAlias;
		}

		return $this->supplies[Stock::ALIAS_SUPPLIER_STORE] ?? null;
	}



	private function getNextSupplierClosingDayFromToday(int $index, int $i = 1): int
	{
		if ($index > 6) {
			$index -= 7;
		}
		$supplierClosingTime = $this->getSupplier()?->getClosingTime();
		$supplierClosingHour = $supplierClosingTime[$index] ?? null;
		if ($supplierClosingHour !== null) {
			return $i;
		}

		return $this->getNextSupplierClosingDayFromToday($index + 1, $i + 1);
	}

	private function formatDateToString(DateTimeImmutable|\DateTimeInterface $date): string|Stringable
	{
		$today = $this->currentDateTimeProvider->getCurrentDateTime()->modify('midnight'); //new DateTimeImmutable('2024-09-04 midnight');
		$calcDate = DateTimeImmutable::createFromInterface($date)->modify('midnight');
		$ts   = $calcDate->getTimestamp();
		$diff = $today->getTimestamp() - $ts;

		if ($diff == 0) {
			return new TranslateData('delivery_today');
		} elseif ($diff < 0) {
			$diff     = abs($diff);
			$day_diff = floor($diff / 86400);
			if ($day_diff == 0) {
				return new TranslateData('delivery_today');
			}
			if ($day_diff == 1) {
				return new TranslateData('delivery_tomorrow');
			}
			if ($day_diff == 2) {
				return new TranslateData('delivery_day_after_tomorrow');
			}

			if ($day_diff < 7) {
				return new TranslateData('delivery_' . Strings::firstLower($calcDate->format('l')));
			}
		}

		return '';
	}

	public function getSupplier(): ?Supplier
	{
		if ($this->getSupplierSupply() === null) {
			return null;
		}

		return $this->supplierModel->getSupplierBySupplyDto($this->getSupplierSupply());
	}


	public static function getOnStockTypes(): array
	{
		return [
			self::TYPE_ON_STOCK,
			self::TYPE_ON_STOCK_SUPPLIER,
			self::TYPE_TO_ORDER,
			self::TYPE_WAITING,
			self::TYPE_PREORDER,
			self::TYPE_TO_DOWNLOAD,
		];
	}

	public static function getOutOfStockType(): array
	{
		return [
			self::TYPE_OUT_OF_STOCK,
		];
	}

	public static function getOutOfStockPermanentlyType(): array
	{
		return [
			self::TYPE_NOT_FOR_SALE,
		];
	}


}
