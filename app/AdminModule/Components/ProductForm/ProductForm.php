<?php

declare(strict_types=1);

namespace App\AdminModule\Components\ProductForm;

use App\AdminModule\Components\ProductForm\DTO\DTO;
use App\AdminModule\Components\ProductForm\Exception\FreeTransportException;
use App\Components\MessageForForm\MessageForForm;
use App\Components\MessageForForm\MessageForFormFactory;
use App\Model\ConfigService;
use App\Model\Currency\CurrencyHelper;
use App\Model\CustomField\SuggestUrls;
use App\Model\Duplicator\ProductDuplicator;
use App\Model\ElasticSearch\Product\Facade;
use App\Model\Image\ImageObjectFactory;
use App\Model\Link\LinkFactory;
use App\Model\Orm\Mutation\Mutation;
use App\Model\Orm\Orm;
use App\Model\Orm\PriceLevel\PriceLevel;
use App\Model\Orm\Product\Product;
use App\Model\Orm\Product\ProductModel;
use App\Model\Orm\Stock\Stock;
use App\Model\Orm\User\User;
use App\Model\Translator;
use App\Model\TranslatorDB;
use Nette\Application\UI\Control;
use Nette\Application\UI\Form;
use Nette\Bridges\ApplicationLatte\DefaultTemplate;
use Nette\Http\IRequest;
use Nette\Utils\ArrayHash;
use Nextras\Orm\Collection\ICollection;

/**
 * @property-read DefaultTemplate $template
 */
final class ProductForm extends Control
{

	public const SUBMIT_MUTATION_CREATE = 'mutationCreate';
	public const SUBMIT_MUTATION_REMOVE = 'mutationRemove';

	/** @var ICollection<Mutation> */
	private ICollection $mutations;

	private mixed $method;

	private array $postData = [];

	private Mutation $defaultMutation;

	public function __construct(
		private readonly Product $product,
		private readonly User $userEntity,
		private readonly string $rsTemplatesPath,
		private readonly SuggestUrls $urls,
		private readonly Orm $orm,
		private readonly LinkFactory $linkFactory,
		private readonly Translator $translator,
		private readonly TranslatorDB $translatorDB,
		private readonly ProductFormBuilder $productFormBuilder,
		private readonly ProductFormSuccess $productFormSuccess,
		private readonly ConfigService $configService,
		private readonly ImageObjectFactory $imageObjectFactory,
		private readonly Facade $productElasticFacade,
		private readonly ProductModel $productModel,
		private readonly ProductDuplicator $productDuplicator,
		private readonly \App\Model\ElasticSearch\All\Facade $allElasticFacade,
		private readonly MessageForFormFactory $messageForFormFactory,
	)
	{
		$this->onAnchor[] = $this->init(...);
	}


	private function init(): void
	{
		$this->method = $this->getPresenter()->request->getMethod();

		if ($this->method === IRequest::Post) {
			$this->postData = $this->getPresenter()->request->getPost();
		}

		$this->mutations = $this->orm->mutation->findAllWithRsOrder();
		$this->defaultMutation = $this->orm->mutation->getDefault();

		$this->productModel->normalizeProduct($this->product);

		$this->orm->persistAndFlush($this->product);
	}


	public function render(): void
	{
		$this->template->object = $this->product;
		$this->template->add('RS_TEMPLATE_DIR', RS_TEMPLATE_DIR);
		$this->template->add('templates', RS_TEMPLATE_DIR);

		$this->template->parameters = $this->orm->parameter->findByProductType($this->product->typeName);

		$this->template->product = $this->product;
		$this->template->mutations = $this->mutations;
		$this->template->tags = $this->product->tagLocalizations->toCollection()->fetchPairs('id');
		$this->template->priceLevels = $this->orm->priceLevel->findBy(['type' => [PriceLevel::TYPE_PURCHASE, PriceLevel::TYPE_RECOMMENDED]]);
		$this->template->currencies = CurrencyHelper::CURRENCIES;
		$this->template->stocks = $this->orm->stock->findBy(['alias' => Stock::ALIAS_SHOP]);

		$this->template->historyPriceLevels = $this->orm->priceLevel->findAllWithDiscountPriceLevel();
		$pricesHistory = [];
		foreach ($this->template->historyPriceLevels as $priceLevel) {
			$pricesHistory[$priceLevel->id] = $this->orm->productVariantPriceLog->findBy(['productVariant' => $this->product->firstVariant, 'priceLevel' => $priceLevel])->orderBy('id');
		}

		$this->template->pricesHistory = $pricesHistory;

		$this->template->orm = $this->orm;
		$this->template->translatorDB = $this->translatorDB;
		$this->template->fileUploadLink = $this->presenter->link('upload!', ['id' => $this->product->id]);

		$this->template->add('imageObjectFactory', $this->imageObjectFactory);

		$this->template->config = $this->configService->getParams();
		$this->template->userEntity = $this->userEntity;

		$linksToFront = [];
		foreach ($this->mutations as $mutation) {
			$productLocalization = $this->product->getLocalization($mutation);
			if ($productLocalization->alias !== null) {
				$linksToFront[$mutation->langCode] = $this->linkFactory->linkTranslateToNette(
					$productLocalization, ['show' => 1, 'mutation' => $mutation]
				);
			}
		}

		$this->template->linksToFront = ($linksToFront);

		$defaultLangs = $this->mutations->limitBy(2)->fetchPairs(null, 'langCode');

		$this->template->defaultMutation = $this->defaultMutation;
		$this->template->defaultLangsString = implode(' ', $defaultLangs);
		$this->template->defaultLangsStringWithLang = implode(' ', array_map(fn($langCode) => 'lang-' . $langCode, $defaultLangs));

		$this->template->defaultMutation = $this->orm->mutation->getDefault();
		$this->template->setTranslator($this->translator);


		$this->template->add('dto', DTO::create(
			rsTemplatesPath: $this->rsTemplatesPath,
		));

		$this->template->urls = $this->urls;
		$this->template->render(__DIR__ . '/productForm.latte');
	}


	protected function createComponentForm(): Form
	{
		$form = new Form();
		$form->setTranslator($this->translator);

		$this->productFormBuilder->build($form, $this->product, $this->mutations, $this->postData);

		$form->onValidate[] = $this->formValidate(...);
		$form->onSuccess[] = [$this, 'formSucceeded'];
		$form->onError[] = [$this, 'formError'];
//		$form->onSubmit[] = [$this, 'formSubmit'];
		return $form;
	}

	private function formValidate(Form $form, ArrayHash $values): void
	{
//		try {
//			$this->productFormValidate->verifyFreeTransport($values, $this->product);
//		} catch (FreeTransportException $exception) {
//			$form->addError($exception->getMessage());
//		}
	}

	public function formError(Form $form): void
	{
//		$this->flashMessage('Error', 'error');
		if ($this->presenter->isAjax()) {
			$this->redrawControl();
		}
	}


	public function formSucceeded(Form $form, ArrayHash $values): void
	{
		$this->productFormSuccess->execute($form, $this->product, $this->userEntity, $values, $this->mutations);

		$this->presenter->redirect('edit', ['id' => $this->product->id]);
	}


	public function handleDelete(): void
	{
		$this->productElasticFacade->deleteFromAllMutationNow($this->product);
		$this->allElasticFacade->deleteNow($this->product);
		$this->orm->product->removeAndFlush($this->product);

		$this->presenter->redirect('Catalog:');
	}


	public function handleDuplicate(): void
	{
		$newProduct = $this->productDuplicator->duplicate($this->product);
		$this->orm->product->persistAndFlush($newProduct);

		$this->productModel->saveToEs($newProduct);

		$this->presenter->redirect('edit', ['id' => $newProduct->id]);
	}

	public function createComponentMessageForForm(): MessageForForm
	{
		return $this->messageForFormFactory->create($this->translator);
	}

}
