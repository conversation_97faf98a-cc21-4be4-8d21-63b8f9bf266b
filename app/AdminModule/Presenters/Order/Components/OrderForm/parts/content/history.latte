{var $props = [
	title: '<PERSON><PERSON><PERSON><PERSON><PERSON>',
	id: 'user',
	icon: $templates.'/part/icons/align-left.svg',
	variant: 'main',
	open: true,
	classes: ['u-mb-xxs'],
	rowMain: false
]}

{embed $templates.'/part/box/toggle.latte', props=>$props, templates=>$templates}
	{block content}
		<h2><PERSON><PERSON><PERSON> obje<PERSON>ky</h2>
		<table>
			<thead>
				<tr>
					<th><PERSON><PERSON><PERSON></th>
					<th>Množství</th>
					<th>Cena / jedn.</th>
					<th>Cena / jedn. DPH</th>
					<th>Cena celkem</th>
					<th>Sazba DPH</th>
					<th>Cena s DPH</th>
				</tr>
			</thead>

			<tbody>
				{foreach $order->getBuyableItems() as $item}
					<tr>
						<td>{$item->getName()}</td>
						<td>{$item->amount}</td>
						<td>{$item->unitPrice->asMoney(4)|money}</td>
						<td>{$item->unitPriceVat|money}</td>
						<td>{$item->totalPrice|money}</td>
						<td>{$item->vatRate->value} <span n:if="$item->vatRateValue !== null">({$item->vatRateValue->toInt()}%)</span></td>
						<td>{$item->totalPriceVat|money}</td>
					</tr>
				{/foreach}
				{*<tr n:foreach="$order->vouchers as $voucher">
					<td>{$voucher->getName()}</td>
					<td>{$voucher->amount}</td>
					<td>{$voucher->unitPrice->asMoney(4)|money}</td>
					<td>{$voucher->unitPriceVat|money}</td>
					<td>{$voucher->totalPrice|money}</td>
					<td>{$voucher->vatRate->value} <span n:if="$voucher->vatRateValue !== null">({$voucher->vatRateValue->toInt()}%)</span></td>
					<td>{$voucher->totalPriceVat|money}</td>
				</tr>
				<tr n:foreach="$order->gifts as $gift">
					<td>{$gift->getName()}</td>
					<td>{$gift->amount}</td>
					<td>{$gift->unitPrice->asMoney(4)|money}</td>
					<td>{$gift->unitPriceVat|money}</td>
					<td>{$gift->totalPrice|money}</td>
					<td>{$gift->vatRate->value} <span n:if="$gift->vatRateValue !== null">({$gift->vatRateValue->toInt()}%)</span></td>
					<td>{$gift->totalPriceVat|money}</td>
				</tr>*}
				<tr>
					<td colspan="4"><strong>Doprava:</strong> {$order->delivery->getName()}</td>
					<td>{$order->delivery->totalPrice|money}</td>
					<td>{$order->delivery->vatRate->value} <span n:if="$order->delivery->vatRateValue !== null">({$order->delivery->vatRateValue->toInt()}%)</span></td>
					<td>{$order->delivery->totalPriceVat|money}</td>
				</tr>
				<tr>
					<td colspan="4"><strong>Platba:</strong> {$order->payment->getName()}</td>
					<td>{$order->payment->totalPrice|money}</td>
					<td>{$order->payment->vatRate->value} <span n:if="$order->payment->vatRateValue !== null">({$order->payment->vatRateValue->toInt()}%)</span></td>
					<td>{$order->payment->totalPriceVat|money}</td>
				</tr>
			</tbody>

			<tfoot>
				<tr>
					<td colspan="5" align="right"><strong>K zaplacení</strong></td>
					<th>
						{$order->getTotalPriceVat(withDelivery: true, includeGiftCertificates: true)|money}
					</th>
				</tr>
			</tfoot>
		</table>

		<h2>Historie objednávky</h2>
		<table>
			<thead>
				<tr>
					<th>Datum a čas</th>
					<th>Původní stav</th>
					<th>Nový stav</th>
				</tr>
			</thead>
			<tbody>
				<tr n:foreach="$order->stateChanges as $stateChange">
					<td>{$stateChange->changedAt|date:'j.n.Y H:i'}</td>
					<td>{$stateChange->from?->value}</td>
					<td>{$stateChange->to->value}</td>
				</tr>
			</tbody>
		</table>
	{/block}
{/embed}



