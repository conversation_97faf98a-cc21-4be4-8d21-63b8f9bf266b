<?php declare(strict_types = 1);

namespace App\AdminModule\Presenters\Order\Components\OrderForm;

use App\Model\Orm\Mutation\Mutation;
use App\Model\Orm\Order\Delivery\DeliveryInformation;
use App\Model\Orm\Order\Delivery\DeliveryType;
use App\Model\Orm\Order\Delivery\LegacyDeliveryInformation;
use App\Model\Orm\Order\Delivery\PhysicalDeliveryInformation;
use App\Model\Orm\Order\Order;
use App\Model\Orm\Orm;
use App\Model\Orm\PriceLevel\PriceLevel;
use App\Model\Orm\Product\Product;
use App\Model\Orm\ProductType\ProductType;
use App\Model\Orm\ProductVariant\ProductVariant;
use App\Model\Orm\State\State;
use App\Model\Orm\Stock\Stock;
use App\PostType\Page\Model\Orm\CatalogTreeModel;
use App\Model\Translator;
use Nette\Application\UI\Form;
use Nette\Forms\Container;
use Nextras\Orm\Collection\ICollection;

final class OrderFormBuilder
{


	public function __construct(
		private readonly Orm $orm,
	)
	{
	}

	public function build(Form $form, Order $order): void
	{
		$this->addOrderCustomerToForm($form, $order);

		$deliveryInformation = $order->delivery->information;
		$deliveryInformationType = $deliveryInformation->getType();
		if ($deliveryInformationType === DeliveryType::Physical) {
			assert($deliveryInformation instanceof PhysicalDeliveryInformation);
			$this->addPhysicalDeliveryInformationToForm($form, $deliveryInformation);
		} elseif ($deliveryInformationType === DeliveryType::Legacy) {
			assert($deliveryInformation instanceof LegacyDeliveryInformation);
			$this->addLegacyDeliveryInformationToForm($form, $deliveryInformation);
		}
	}

	private function addOrderCustomerToForm(Form $form, Order $order): void
	{
		$container = $form->addContainer('orderCustomer');
		$container->addText('email', 'email')->setDefaultValue($order->email);
		$container->addText('name', 'name')->setDefaultValue($order->name);
		$container->addText('street', 'street')->setDefaultValue($order->street);
		$container->addText('city', 'city')->setDefaultValue($order->city);
		$container->addText('zip', 'zip')->setDefaultValue($order->zip);
		$container->addText('phone', 'phone')->setDefaultValue($order->phone);
		$container->addText('companyName', 'companyName')->setDefaultValue($order->companyName);
		$container->addText('companyIdentifier', 'companyIdentifier')->setDefaultValue($order->companyIdentifier);
		$container->addText('vatNumber', 'vatNumber')->setDefaultValue($order->vatNumber);
		$container->addTextArea('note', 'note')->setDefaultValue($order->note);
		$container->addSelect('country', 'state', $this->orm->state->findAll()->fetchPairs('id', 'name'))->setDefaultValue($order->country->id);
	}

	private function addPhysicalDeliveryInformationToForm(Form $form, PhysicalDeliveryInformation $deliveryInformation): void
	{
		$container = $form->addContainer('orderDeliveryInformation');
		$container->addText('company', 'company')
			->setDefaultValue($deliveryInformation->company);
		$container->addText('name', 'name')
			->setDefaultValue($deliveryInformation->name);
		$container->addText('phoneNumber', 'phoneNumber')
			->setDefaultValue($deliveryInformation->phoneNumber);
		$container->addText('street', 'street')
			->setDefaultValue($deliveryInformation->street);
		$container->addText('city', 'city')
			->setDefaultValue($deliveryInformation->city);
		$container->addText('zip', 'zip')
			->setDefaultValue($deliveryInformation->zip);
		$container->addSelect('country', 'state', $this->orm->state->findAll()->fetchPairs('id', 'name'))
			->setDefaultValue($deliveryInformation->country->id);
		$container->addText('trackingCode', 'trackingCode')
			->setDefaultValue($deliveryInformation->trackingCode);
	}

	private function addLegacyDeliveryInformationToForm(Form $form, LegacyDeliveryInformation $deliveryInformation): void
	{
		$container = $form->addContainer('orderDeliveryInformation');
		$container->addText('phoneNumber', 'phoneNumber')
			->setDefaultValue($deliveryInformation->phoneNumber);
		$container->addText('trackingCode', 'trackingCode')
			->setDefaultValue($deliveryInformation->trackingCode);
		$container->addText('company', 'company')
			->setDefaultValue($deliveryInformation->company);
		$container->addText('name', 'name')
			->setDefaultValue($deliveryInformation->name);
		$container->addText('street', 'street')
			->setDefaultValue($deliveryInformation->street);
		$container->addText('city', 'city')
			->setDefaultValue($deliveryInformation->city);
		$container->addText('zip', 'zip')
			->setDefaultValue($deliveryInformation->zip);
		$container->addSelect('country', 'state', $this->orm->state->findAll()->fetchPairs('id', 'name'))
			->setDefaultValue($deliveryInformation->country->id);
		$container->addText('pickupPointId', 'pickupPointId')
			->setDefaultValue($deliveryInformation->pickupPointId);
	}

}
