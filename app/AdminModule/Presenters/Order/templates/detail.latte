{layout $templates.'/@layout-new.latte'}
{varType App\Model\Orm\Order\Order $order}

{block #content}
<div class="main__main main__main--one-column">
	<div class="main__header">
		{include $templates.'/part/box/header.latte',
			props: [
				title: $translator->translate('Orders') . ': ' . $order->orderNumber,
				isPageTitle: true,
			]
		}
	</div>
	<div class="main__content scroll">
		{snippet flash}
			<div n:foreach="$flashes as $flash" class="message message-{$flash->type}">{$flash->message}</div>
		{/snippet}
		<p class="u-mb-xs">
			<b>Datum objednávky:</b>
			{$order->placedAt|date:'d.m.Y H:i:s'}
		</p>
		{control orderForm}
	</div>
</div>
